/**
 * Diagnostic & Auto-Fix Module JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize diagnostic module
    const DiagnosticAutoFix = {

        // Current scan results
        currentResults: null,

        // Progress modal instance
        progressModal: null,

        // Layout state for preventing shifts
        layoutState: null,

        /**
         * Initialize the module
         */
        init: function() {
            // Initialize components immediately - no loading screen
            this.bindEvents();
            this.initProgressModal();
            this.handleExistingNotices();
            this.initializeEmergencyRecovery();
            this.loadRecentFixes(); // Load recent fixes on initialization
            this.initializeAutoFixButtonState(); // Initialize button state on page load
            this.initHealthScoreCircle(); // Initialize health score circle animation

            // REMOVED: No automatic fresh scan on page load
            // Fresh scans will be triggered by user actions only

            // Show debug toast notifications only if debug mode is enabled
            if (redcoDiagnosticAjax.debug_toast_notifications) {
                // Show test toast immediately to verify system
                setTimeout(() => {
                    this.showToast(
                        'System Ready',
                        'Diagnostic & Auto-Fix module loaded successfully. Toast notification system is active.',
                        'success',
                        5000
                    );
                }, 500);

                // Show informational toast about layout stability after a short delay
                setTimeout(() => {
                    this.showToast(
                        'Layout Stability Info',
                        'During diagnostic operations, the layout may temporarily adjust. This is normal behavior and will stabilize once operations complete.',
                        'info',
                        8000
                    );
                }, 2000);

            } else {
            }
        },

        /**
         * Bind event handlers - Static elements only
         */
        bindEvents: function() {
            // Static action buttons (always present)
            $('#run-comprehensive-scan').on('click', this.handleComprehensiveScan.bind(this));
            $('#apply-auto-fixes').on('click', this.handleAutoFix.bind(this));
            $('#emergency-mode-toggle').on('click', this.handleEmergencyMode.bind(this));
            $('#export-diagnostic-report').on('click', this.handleExportReport.bind(this));

            // Static sidebar actions
            $('#sidebar-apply-fixes').on('click', this.handleAutoFix.bind(this));
            $('#sidebar-export-report').on('click', this.handleExportReport.bind(this));

            // Static emergency mode controls
            $('#activate-emergency-mode, #deactivate-emergency-mode').on('click', this.handleEmergencyMode.bind(this));

            // Static refresh buttons for recent fixes only
            $('#refresh-recent-fixes').on('click', this.loadRecentFixes.bind(this));
            $('.refresh-recent-fixes').on('click', this.loadRecentFixes.bind(this));
            $('#refresh-fix-history').on('click', this.loadRecentFixes.bind(this));

            // Set up event delegation for ALL dynamic content
            this.setupEventDelegation();
        },

        /**
         * Initialize health score circle animation
         */
        initHealthScoreCircle: function() {
            const $healthCircle = $('#header-health-score');
            if ($healthCircle.length === 0) return;

            const score = parseInt($healthCircle.data('score')) || 0;

            // Set CSS custom properties for animation
            $healthCircle.css('--score-percentage', score);

            // Determine color based on score - sophisticated color scheme for better contrast on green header
            let color = '#E53E3E'; // Red for poor scores (0-49)
            if (score >= 90) color = '#3182CE'; // Strong blue for excellent (90-100)
            else if (score >= 75) color = '#38B2AC'; // Teal for good (75-89)
            else if (score >= 50) color = '#DD6B20'; // Orange for fair (50-74)

            $healthCircle.css('--health-score-color', color);

            // Trigger animation after a short delay
            setTimeout(() => {
                $healthCircle.addClass('animated');
            }, 500);
        },

        /**
         * Update health score circle with new score
         */
        updateHealthScoreCircle: function(newScore, trend = 0) {
            const $healthCircle = $('#header-health-score');
            const $scoreValue = $('#header-score-value');
            const $scoreTrend = $('#header-score-trend');
            const $lastUpdated = $('#header-last-updated');

            if ($healthCircle.length === 0) return;

            // Update score value
            $scoreValue.text(newScore);
            $healthCircle.attr('data-score', newScore);

            // Update CSS custom properties
            $healthCircle.css('--score-percentage', newScore);

            // Determine new color - sophisticated color scheme for better contrast on green header
            let color = '#E53E3E'; // Red for poor scores (0-49)
            if (newScore >= 90) color = '#3182CE'; // Strong blue for excellent (90-100)
            else if (newScore >= 75) color = '#38B2AC'; // Teal for good (75-89)
            else if (newScore >= 50) color = '#DD6B20'; // Orange for fair (50-74)

            $healthCircle.css('--health-score-color', color);

            // Update trend
            if (trend > 0) {
                $scoreTrend.html('<span class="trend-up">↗ +' + trend + '</span>');
            } else if (trend < 0) {
                $scoreTrend.html('<span class="trend-down">↘ ' + trend + '</span>');
            } else {
                $scoreTrend.html('<span class="trend-neutral">→ 0</span>');
            }

            // Update last updated time
            $lastUpdated.text('Just updated');

            // Trigger update animation
            $healthCircle.removeClass('animated').addClass('score-updated');
            setTimeout(() => {
                $healthCircle.addClass('animated').removeClass('score-updated');
            }, 100);
        },

        /**
         * Set up event delegation for dynamic content (called once)
         */
        setupEventDelegation: function() {
            // Use event delegation for all dynamic elements
            $(document).on('click', '.fix-single-issue', this.handleSingleFix.bind(this));
            $(document).on('click', '.rollback-fix', this.handleRollback.bind(this));
            $(document).on('click', '#show-all-issues', this.showAllIssues.bind(this));
            $(document).on('click', '.toggle-how-to-solve', this.toggleHowToSolve.bind(this));
            $(document).on('click', '#run-new-scan', (e) => {
                e.preventDefault();
                this.runDiagnosticScan('comprehensive');
            });
        },

        /**
         * Initialize progress modal
         */
        initProgressModal: function() {
            // Create progress modal HTML if it doesn't exist
            if ($('#diagnostic-progress-modal').length === 0) {
                const modalHtml = `
                    <div id="diagnostic-progress-modal" class="redco-modal" style="display: none;">
                        <div class="redco-modal-content">
                            <div class="redco-modal-header">
                                <h3 id="progress-modal-title">Processing...</h3>
                            </div>
                            <div class="redco-modal-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-text">Initializing...</div>
                                </div>
                                <div class="progress-steps" id="progress-steps"></div>
                            </div>
                            <div class="redco-modal-footer">
                                <button type="button" class="button button-secondary" id="close-progress-modal" style="display: none;">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);
            }
        },

        /**
         * Handle existing admin notices to prevent layout shifts
         */
        handleExistingNotices: function() {
            // Aggressively find and remove any admin notices that could cause layout shifts
            const $notices = $('.notice, .updated, .error, .notice-success, .notice-error, .notice-warning, .notice-info').not('.redco-toast');

            if ($notices.length > 0) {
                $notices.each(function() {
                    const $notice = $(this);

                    // Extract notice content before hiding
                    const noticeText = $notice.find('p').text().trim();
                    const isError = $notice.hasClass('notice-error') || $notice.hasClass('error');
                    const isSuccess = $notice.hasClass('notice-success') || $notice.hasClass('updated');
                    const isWarning = $notice.hasClass('notice-warning');

                    // Always hide the notice to prevent layout shifts
                    $notice.hide();

                    // Convert Redco-related notices to toast notifications
                    if (noticeText && (noticeText.includes('Redco Optimizer') || noticeText.includes('redco'))) {
                        let toastType = 'info';
                        if (isError) toastType = 'error';
                        else if (isSuccess) toastType = 'success';
                        else if (isWarning) toastType = 'warning';

                        // Show toast after a short delay
                        setTimeout(() => {
                            this.showToast(
                                'System Notice',
                                noticeText,
                                toastType,
                                6000
                            );
                        }, 500);

                    }
                }.bind(this));
            }

            // Set up a mutation observer to catch any notices added after page load
            this.setupNoticeObserver();
        },

        /**
         * Set up mutation observer to catch dynamically added notices
         */
        setupNoticeObserver: function() {
            if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);
                                if ($node.hasClass('notice') || $node.hasClass('updated') || $node.hasClass('error')) {
                                    $node.hide();
                                }
                            }
                        });
                    });
                });

                // Observe the document body for added notices
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

            }
        },

        /**
         * Handle comprehensive scan action
         */
        handleComprehensiveScan: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);

            // Add loading state
            this.setButtonLoading($button, true);
            this.disableAllButtons(true);

            this.runDiagnosticScan('comprehensive');
        },

        /**
         * Set button loading state
         */
        setButtonLoading: function($button, loading) {
            if (loading) {
                $button.addClass('loading').prop('disabled', true);
            } else {
                $button.removeClass('loading').prop('disabled', false);
            }
        },

        /**
         * Disable/enable all buttons
         */
        disableAllButtons: function(disable) {
            const buttons = $('.button, #emergency-mode-toggle, #export-diagnostic-report, #run-comprehensive-scan');
            const $autoFixButton = $('#apply-auto-fixes');

            if (disable) {
                // Store original layout state
                this.storeLayoutState();

                // Store the current state of Apply Auto-Fixes button
                this.autoFixButtonWasDisabled = $autoFixButton.prop('disabled');

                buttons.each((index, button) => {
                    const $btn = $(button);
                    $btn.prop('disabled', true).addClass('disabled');
                });

                // Also disable Apply Auto-Fixes button during processing
                $autoFixButton.prop('disabled', true).addClass('disabled');

                // Add processing class to main container to maintain layout
                $('.diagnostic-layout').addClass('processing');
            } else {
                buttons.each((index, button) => {
                    const $btn = $(button);
                    $btn.prop('disabled', false).removeClass('disabled loading');
                });

                // For Apply Auto-Fixes button, restore its previous state or keep it disabled if it was disabled before
                // Don't automatically enable it - let updateAutoFixButtonState handle this
                $autoFixButton.removeClass('disabled loading');
                if (this.autoFixButtonWasDisabled !== undefined) {
                    // Restore previous state only if we stored it
                    $autoFixButton.prop('disabled', this.autoFixButtonWasDisabled);
                    this.autoFixButtonWasDisabled = undefined;
                }

                // Remove processing class
                $('.diagnostic-layout').removeClass('processing');

                // Restore layout state
                this.restoreLayoutState();
            }
        },

        /**
         * Store layout state to prevent shifting
         */
        storeLayoutState: function() {
            const $main = $('.redco-content-main');
            const $sidebar = $('.redco-content-sidebar');

            // Store current dimensions
            this.layoutState = {
                mainWidth: $main.outerWidth(),
                sidebarWidth: $sidebar.outerWidth(),
                containerWidth: $('.diagnostic-layout').outerWidth()
            };

            // Apply fixed dimensions
            $main.css('width', this.layoutState.mainWidth + 'px');
            $sidebar.css('width', this.layoutState.sidebarWidth + 'px');
        },

        /**
         * Restore layout state
         */
        restoreLayoutState: function() {
            if (this.layoutState) {
                // Remove fixed dimensions
                $('.redco-content-main').css('width', '');
                $('.redco-content-sidebar').css('width', '');
                this.layoutState = null;
            }
        },

        /**
         * Show toast notification
         */
        showToast: function(title, message, type = 'info', duration = 5000) {
            // Use global toast notification system only
            if (typeof showToast === 'function') {
                const combinedMessage = title ? `${title}: ${message}` : message;
                showToast(combinedMessage, type, duration);
            }
        },

        /**
         * Hide toast notification
         */
        hideToast: function(toastId) {
            const toast = $('#' + toastId);
            if (toast.length) {
                toast.removeClass('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        },

        /**
         * Run diagnostic scan
         */
        runDiagnosticScan: function(scanType) {
            this.showProgressModal('Running ' + scanType.charAt(0).toUpperCase() + scanType.slice(1) + ' Scan');
            this.updateProgress(0, 'Initializing scan...');

            const data = {
                action: 'redco_run_diagnostic_scan',
                scan_type: scanType,
                include_pagespeed: $('#pagespeed_api_key').val() !== '',
                nonce: redcoDiagnosticAjax.nonce
            };

            // Simulate progress updates and store interval ID
            this.progressInterval = this.simulateProgress([
                { progress: 20, text: 'Scanning WordPress core...' },
                { progress: 40, text: 'Analyzing database performance...' },
                { progress: 60, text: 'Checking frontend optimization...' },
                { progress: 80, text: 'Evaluating server configuration...' },
                { progress: 95, text: 'Generating recommendations...' }
            ]);

            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: data,
                success: (response) => {
                    // Clear the progress simulation
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    this.updateProgress(100, 'Scan completed!');

                    setTimeout(() => {
                        if (response.success) {
                            this.currentResults = response.data;
                            this.displayScanResults(response.data);
                            this.updateSidebarStatsAfterScan(response.data); // Update sidebar statistics
                            this.hideProgressModal();
                            this.disableAllButtons(false); // Re-enable buttons
                            this.showSuccess(`Scan completed! Found ${response.data.issues_found} issues (${response.data.critical_issues} critical, ${response.data.auto_fixable} auto-fixable)`);
                        } else {
                            this.showError('Scan failed: ' + response.data);
                            this.disableAllButtons(false); // Re-enable buttons on error
                        }
                    }, 500);
                },
                error: (xhr, status, error) => {
                    // Clear the progress simulation on error too
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    let errorMessage = 'Network error occurred during scan';
                    if (xhr.status === 0) {
                        errorMessage = 'Connection failed - please check your internet connection';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied - please refresh the page and try again';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error occurred - please try again later';
                    } else if (xhr.status === 504) {
                        errorMessage = 'Request timeout - the scan took too long to complete';
                    }

                    this.showToast(
                        'Scan Failed',
                        errorMessage + ` (Error ${xhr.status}: ${error})`,
                        'error',
                        10000
                    );

                    this.hideProgressModal();
                    this.disableAllButtons(false); // Re-enable buttons on error
                }
            });
        },

        /**
         * Handle auto-fix action
         */
        handleAutoFix: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);

            if (!this.currentResults || !this.currentResults.auto_fixable || this.currentResults.auto_fixable === 0) {
                this.showError('No auto-fixable issues found. Please run a scan first.');
                return;
            }

            if (!confirm('Are you sure you want to apply auto-fixes? A backup will be created before making changes.')) {
                return;
            }

            // Add loading state
            this.setButtonLoading($button, true);
            this.disableAllButtons(true);

            // Mark auto-fixable issues as processing
            this.markAutoFixableIssuesAsProcessing();

            this.showProgressModal('Applying Auto-Fixes');
            this.updateProgress(0, 'Creating backup...');

            // Set emergency timeout as failsafe (70 seconds - longer than AJAX timeout)
            this.emergencyTimeout = setTimeout(() => {
                console.warn('🚨 Emergency timeout triggered - auto-fix took too long');
                this.emergencyRecovery();
            }, 70000);

            const data = {
                action: 'redco_apply_auto_fixes',
                nonce: redcoDiagnosticAjax.nonce
            };

            // Simulate fix progress and store interval ID
            this.progressInterval = this.simulateProgress([
                { progress: 20, text: 'Creating backup...' },
                { progress: 40, text: 'Applying configuration fixes...' },
                { progress: 60, text: 'Optimizing database settings...' },
                { progress: 80, text: 'Updating server configuration...' },
                { progress: 95, text: 'Verifying changes...' }
            ]);

            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: data,
                timeout: 60000, // 60 second timeout to prevent infinite hanging
                success: (response) => {
                    // Clear emergency timeout
                    if (this.emergencyTimeout) {
                        clearTimeout(this.emergencyTimeout);
                        this.emergencyTimeout = null;
                    }

                    // Clear the progress simulation
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    this.updateProgress(100, 'Fixes applied successfully!');

                    setTimeout(() => {
                        if (response.success) {
                            this.processFixResults(response.data);
                            this.displayFixResults(response.data);

                            // Enhanced success message with details
                            let successMessage = `Applied ${response.data.fixes_applied} fixes successfully!`;
                            if (response.data.fixes_failed > 0) {
                                successMessage += ` ${response.data.fixes_failed} fixes failed.`;
                            }
                            if (response.data.backup_created) {
                                successMessage += ' Backup created for rollback.';
                            }

                            this.showToast(
                                'Bulk Auto-Fix Completed',
                                successMessage,
                                response.data.fixes_failed > 0 ? 'warning' : 'success',
                                10000
                            );

                            this.hideProgressModal();
                            this.disableAllButtons(false); // Re-enable buttons
                            this.updateStatistics(response.data);

                            // Update Apply Auto-Fixes button state after bulk fix
                            this.updateAutoFixButtonStateAfterBulkFix();

                            // Refresh recent fixes to show the new fix session
                            this.loadRecentFixes();
                        } else {
                            this.showToast(
                                'Bulk Auto-Fix Failed',
                                'Auto-fix failed: ' + response.data,
                                'error',
                                8000
                            );
                            this.markAutoFixableIssuesAsFailed();
                            this.hideProgressModal();
                            this.disableAllButtons(false); // Re-enable buttons on error
                        }
                    }, 500);
                },
                error: (xhr, status, error) => {
                    // Clear emergency timeout
                    if (this.emergencyTimeout) {
                        clearTimeout(this.emergencyTimeout);
                        this.emergencyTimeout = null;
                    }

                    // Clear the progress simulation on error too
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    // CRITICAL FIX: Hide progress modal on error
                    this.hideProgressModal();

                    let errorMessage = 'Network error occurred during auto-fix';
                    if (xhr.status === 0) {
                        errorMessage = 'Connection failed - please check your internet connection';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied - please refresh the page and try again';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error occurred - please try again later';
                    } else if (xhr.status === 504) {
                        errorMessage = 'Request timeout - the auto-fix took too long to complete';
                    }

                    this.showToast(
                        'Auto-Fix Failed',
                        errorMessage + ` (Error ${xhr.status}: ${error})`,
                        'error',
                        10000
                    );

                    this.markAutoFixableIssuesAsFailed();
                    this.disableAllButtons(false); // Re-enable buttons on error
                }
            });
        },

        /**
         * Handle emergency mode toggle
         */
        handleEmergencyMode: function(e) {
            e.preventDefault();

            const isActive = $(e.currentTarget).hasClass('emergency-active') ||
                           $(e.currentTarget).attr('id') === 'deactivate-emergency-mode';
            const action = isActive ? 'deactivate' : 'activate';

            if (!confirm(`Are you sure you want to ${action} emergency mode?`)) {
                return;
            }

            const data = {
                action: 'redco_emergency_mode',
                emergency_action: action,
                nonce: redcoDiagnosticAjax.nonce
            };

            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: data,
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(response.data.message);
                        // Refresh the page to show updated status
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        this.showError('Emergency mode action failed: ' + response.data);
                    }
                },
                error: () => {
                    this.showError('Network error occurred');
                }
            });
        },

        /**
         * Handle export report
         */
        handleExportReport: function(e) {
            e.preventDefault();

            // Create a form and submit it to trigger download
            const form = $('<form>', {
                method: 'POST',
                action: redcoDiagnosticAjax.ajaxurl
            });

            form.append($('<input>', { type: 'hidden', name: 'action', value: 'redco_export_diagnostic_report' }));
            form.append($('<input>', { type: 'hidden', name: 'nonce', value: redcoDiagnosticAjax.nonce }));

            $('body').append(form);
            form.submit();
            form.remove();
        },

        /**
         * Mark auto-fixable issues as processing
         */
        markAutoFixableIssuesAsProcessing: function() {
            $('.issue-item').each(function() {
                const $item = $(this);
                if ($item.find('.fix-single-issue').length > 0) {
                    $item.addClass('processing');
                    $item.find('.issue-actions-right').append('<div class="issue-status-message processing">Processing fix...</div>');
                }
            });
        },

        /**
         * Mark auto-fixable issues as failed
         */
        markAutoFixableIssuesAsFailed: function() {
            $('.issue-item.processing').each(function() {
                const $item = $(this);
                $item.removeClass('processing').addClass('failed');
                $item.find('.issue-status-message').removeClass('processing').addClass('error').text('Fix failed');
            });
        },

        /**
         * Process fix results and update issue states
         */
        processFixResults: function(results) {
            if (results.fix_details && results.fix_details.length > 0) {
                results.fix_details.forEach(fix => {
                    const $issueItem = $(`.issue-item[data-issue-id="${fix.issue_id}"]`);
                    if ($issueItem.length > 0) {
                        $issueItem.removeClass('processing');
                        if (fix.success) {
                            $issueItem.addClass('resolved');
                            $issueItem.find('.issue-status-message').removeClass('processing').addClass('success').text(fix.message || 'Fixed successfully');
                            $issueItem.find('.fix-single-issue').prop('disabled', true).text('Fixed');
                        } else {
                            $issueItem.addClass('failed');
                            $issueItem.find('.issue-status-message').removeClass('processing').addClass('error').text(fix.message || 'Fix failed');
                        }
                    }
                });
            }
        },

        /**
         * Handle single issue fix
         */
        handleSingleFix: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const issueId = $button.data('issue-id');
            const $issueItem = $button.closest('.issue-item');

            if (!confirm('Apply fix for this issue? A backup will be created before making changes.')) {
                return;
            }

             // Add loading state
            this.setButtonLoading($button, true);
            $issueItem.addClass('processing');

            // Remove any existing status messages
            $issueItem.find('.issue-status-message').remove();
            $issueItem.find('.issue-actions-right').append('<div class="issue-status-message processing">Processing fix...</div>');

            // Show progress modal
            this.showProgressModal('Applying Single Fix');
            this.updateProgress(0, 'Preparing to fix issue...');

            const data = {
                action: 'redco_apply_single_fix',
                issue_id: issueId,
                nonce: redcoDiagnosticAjax.nonce
            };

            // Simulate progress for single fix and store interval ID
            this.progressInterval = this.simulateProgress([
                { progress: 30, text: 'Creating backup...' },
                { progress: 60, text: 'Applying fix...' },
                { progress: 90, text: 'Verifying changes...' }
            ]);

            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: data,
                success: (response) => {
  
                    // Clear the progress simulation
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    this.updateProgress(100, 'Fix completed!');

                    setTimeout(() => {
                        if (response.success) {
                            // Mark issue as resolved in UI
                            $issueItem.removeClass('processing').addClass('resolved');
                            $issueItem.find('.issue-status-message').removeClass('processing').addClass('success').text(response.data.message);
                            $button.prop('disabled', true).text('Fixed');

                            // Add data attribute to track fix status
                            $issueItem.attr('data-fix-status', 'resolved');
                            $issueItem.attr('data-fix-timestamp', Date.now());

                            // Show success message with persistence info
                            let successMessage = response.data.message;
                            if (response.data.persistence && response.data.persistence.persisted) {
                                successMessage += ' (Persistence verified)';
                            } else if (response.data.persistence && !response.data.persistence.persisted) {
                                successMessage += ' (Warning: Fix may not persist - ' + response.data.persistence.reason + ')';
                            }

                            this.showToast(
                                'Fix Applied Successfully',
                                successMessage,
                                'success',
                                8000
                            );

                            // Update statistics
                            this.updateSingleFixStatistics();

                            // Update Apply Auto-Fixes button state after single fix
                            this.updateAutoFixButtonStateAfterSingleFix();

                            // Refresh recent fixes to show the new fix
                            this.loadRecentFixes();

                            this.hideProgressModal();
                        } else {
                            console.error('❌ Single fix failed:', response.data);
                            $issueItem.removeClass('processing').addClass('failed');
                            $issueItem.find('.issue-status-message').removeClass('processing').addClass('error').text(response.data);
                            $issueItem.attr('data-fix-status', 'failed');

                            this.showToast(
                                'Fix Failed',
                                'Fix failed: ' + response.data,
                                'error',
                                8000
                            );

                            this.hideProgressModal();
                        }
                    }, 500);
                },
                error: (xhr, status, error) => {
                    console.error('❌ Single fix AJAX error:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    // Clear the progress simulation on error too
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                        this.progressInterval = null;
                    }

                    $issueItem.removeClass('processing').addClass('failed');
                    $issueItem.find('.issue-status-message').removeClass('processing').addClass('error').text('Network error');

                    let errorMessage = 'Network error occurred during fix';
                    if (xhr.responseText) {
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            if (errorResponse.data) {
                                errorMessage = 'Error: ' + errorResponse.data;
                            }
                        } catch (e) {
                            errorMessage = 'Server error: ' + xhr.status + ' ' + xhr.statusText;
                        }
                    }

                    this.showError(errorMessage);
                },
                complete: () => {
                    // Remove loading state from button
                    this.setButtonLoading($button, false);
                    this.hideProgressModal();
                }
            });
        },

        /**
         * Handle rollback
         */
        handleRollback: function(e) {
            e.preventDefault();
            const backupId = $(e.currentTarget).data('backup-id');

            if (!confirm('Are you sure you want to rollback these fixes? This will restore the previous state.')) {
                return;
            }

            const data = {
                action: 'redco_rollback_fixes',
                backup_id: backupId,
                nonce: redcoDiagnosticAjax.nonce
            };

            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: data,
                success: (response) => {
                    if (response.success) {
                        this.showSuccess(response.data.message);
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        this.showError('Rollback failed: ' + response.data);
                    }
                },
                error: () => {
                    this.showError('Network error occurred during rollback');
                }
            });
        },

        /**
         * Show all issues
         */
        showAllIssues: function(e) {
            e.preventDefault();

            if (!this.currentResults || !this.currentResults.issues) {
                console.error('❌ No current results available to show more issues');
                return;
            }

            const $button = $(e.currentTarget);
            const $issuesList = $('#diagnostic-issues-list');

            // Remove the "show more" button
            $button.closest('.show-more-issues').remove();

            // Render all remaining issues (beyond the first 10)
            const allIssues = this.currentResults.issues;
            const remainingIssues = allIssues.slice(10); // Get issues from index 10 onwards

            remainingIssues.forEach(issue => {
                const issueHtml = this.createIssueHtml(issue);
                $issuesList.append(issueHtml);
            });

            // Show a toast notification
            this.showToast(
                'All Issues Displayed',
                `Now showing all ${allIssues.length} issues found in the last scan.`,
                'info',
                3000
            );
        },

        /**
         * Load recent fixes
         */
        loadRecentFixes: function(e) {
            if (e) e.preventDefault();

 
            // Handle both sidebar and main content containers
            const $sidebarContainer = $('#recent-fixes-container');
            const $mainContainer = $('#fix-history-list');
            const $refreshBtn = $('#refresh-recent-fixes');

            // Show loading state for both containers if they exist
            const loadingHtml = `
                <div class="loading-spinner" style="text-align: center; padding: 20px;">
                    <span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span>
                    <p>Loading recent fixes...</p>
                </div>
            `;

            if ($sidebarContainer.length) {
                $sidebarContainer.html(loadingHtml);
            }
            if ($mainContainer.length) {
                $mainContainer.html(loadingHtml);
            }

            // Disable refresh button
            $refreshBtn.prop('disabled', true);

             $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_load_recent_fixes',
                    nonce: redcoDiagnosticAjax.nonce
                },
                success: (response) => {
 
                    if (response.success) {
                        // Update both containers with the same content
                        if ($sidebarContainer.length) {
                            $sidebarContainer.html(response.data.html);
                        }
                        if ($mainContainer.length) {
                            $mainContainer.html(response.data.html);
                        }
                    } else {
                        const errorHtml = '<div class="no-fixes-message"><p>Failed to load recent fixes.</p></div>';
                        if ($sidebarContainer.length) {
                            $sidebarContainer.html(errorHtml);
                        }
                        if ($mainContainer.length) {
                            $mainContainer.html(errorHtml);
                        }
                        console.error('❌ Failed to load recent fixes:', response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error loading recent fixes:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });

                    const errorHtml = '<div class="no-fixes-message"><p>Error loading recent fixes.</p></div>';
                    if ($sidebarContainer.length) {
                        $sidebarContainer.html(errorHtml);
                    }
                    if ($mainContainer.length) {
                        $mainContainer.html(errorHtml);
                    }
                },
                complete: () => {
                    // Re-enable refresh button
                    $refreshBtn.prop('disabled', false);
                }
            });
        },

        /**
         * Update issues list with remaining issues
         */
        updateIssuesList: function(issues) {
            const $issuesList = $('#diagnostic-issues-list');

            if (!issues || issues.length === 0) {
                this.showNoIssuesState();
                return;
            }

            let html = '';
            issues.forEach((issue, index) => {
                html += this.generateIssueHTML(issue, index);
            });

            $issuesList.html(html);

            // Re-bind event handlers for new elements
            this.bindDynamicEvents();
        },

        /**
         * Generate HTML for a single issue - Matches PHP template with right-aligned button
         */
        generateIssueHTML: function(issue, index) {
            const severityIcon = this.getSeverityIcon(issue.severity);
            const statusIndicator = issue.auto_fixable ?
                '<span class="status-indicator success"><span class="dashicons dashicons-yes-alt"></span>Auto-Fixable</span>' :
                '<span class="status-indicator warning"><span class="dashicons dashicons-admin-tools"></span>Manual Fix Required</span>';

            let actionButtons = '';
            if (issue.auto_fixable) {
                actionButtons = `
                    <div class="issue-actions-right">
                        <button type="button" class="action-button primary fix-single-issue" data-issue-id="${issue.id}">
                            <span class="dashicons dashicons-admin-tools"></span>
                            Fix Now
                        </button>
                    </div>
                `;
            } else {
                actionButtons = `
                    <div class="issue-actions-right">
                        <button type="button" class="toggle-how-to-solve" data-issue-id="${issue.id || index}">
                            <span class="dashicons dashicons-arrow-down"></span>
                            How to solve
                        </button>
                    </div>
                `;
            }

            return `
                <div class="issue-item ${issue.severity}" data-issue-id="${issue.id || index}">
                    ${severityIcon}
                    <div class="issue-content">
                        <div class="issue-title">${issue.title}</div>
                        <div class="issue-description">${issue.description}</div>
                        <div class="issue-meta">
                            <span class="issue-category">${issue.category.charAt(0).toUpperCase() + issue.category.slice(1)}</span>
                            <span class="issue-severity">${issue.severity.charAt(0).toUpperCase() + issue.severity.slice(1)}</span>
                            ${statusIndicator}
                        </div>
                        ${!issue.auto_fixable ? `
                            <div class="how-to-solve-tip" id="how-to-solve-${issue.id || index}" style="display: none;">
                                <div class="loading-content">
                                    <span class="dashicons dashicons-update"></span>
                                    Loading help content...
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    ${actionButtons}
                </div>
            `;
        },

        /**
         * Get severity icon HTML
         */
        getSeverityIcon: function(severity) {
            const icons = {
                'critical': '<span class="dashicons dashicons-dismiss"></span>',
                'high': '<span class="dashicons dashicons-warning"></span>',
                'medium': '<span class="dashicons dashicons-info"></span>',
                'low': '<span class="dashicons dashicons-lightbulb"></span>'
            };
            return icons[severity] || icons['low'];
        },

        /**
         * Show no issues state
         */
        showNoIssuesState: function() {
 
            // Find the scan results container
            const $scanResultsContainer = $('.redco-content-main');

            // Create no issues card HTML
            const noIssuesCardHtml = `
                <div class="redco-card">
                    <div class="card-header">
                        <h3>
                            <span class="dashicons dashicons-yes-alt"></span>
                            Scan Results
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="no-issues-found">
                            <div class="success-message">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <h4>Great! No Issues Found</h4>
                                <p>Your website appears to be well-optimized. The last scan completed successfully and found no issues that need attention.</p>
                                <p class="scan-info">
                                    <strong>Last Scan:</strong> Just completed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Find and replace the existing scan results card
            const $existingCard = $scanResultsContainer.find('.redco-card').has('#diagnostic-issues-list, .no-issues-found').first();

            if ($existingCard.length > 0) {
                $existingCard.replaceWith(noIssuesCardHtml);
            } else {
                // If no existing card found, append after the overview section
                $scanResultsContainer.find('.diagnostic-overview').after(noIssuesCardHtml);
            }

            // Update Apply Auto-Fixes button state (no issues)
            this.updateAutoFixButtonState({ issues: [] });
        },

        /**
         * Update statistics display
         */
        updateStatistics: function(stats) {
            if (!stats) return;

            // Update overview stats if they exist
            const $overviewStats = $('.overview-stat .stat-value');
            if ($overviewStats.length >= 6) {
                $overviewStats.eq(2).text(stats.issues_found || 0); // Issues Found
                $overviewStats.eq(3).text(stats.critical_issues || 0); // Critical Issues
                $overviewStats.eq(4).text(stats.auto_fixable_issues || 0); // Auto-Fixable
                $overviewStats.eq(5).text(stats.fixes_applied || 0); // Fixes Applied
            }

            // Update sidebar stats
            $('.sidebar-stat').each(function() {
                const $this = $(this);
                const label = $this.find('.stat-label').text().toLowerCase();

                if (label.includes('auto-fix') && stats.auto_fix_enabled !== undefined) {
                    $this.find('.stat-value').text(stats.auto_fix_enabled ? 'Enabled' : 'Disabled');
                }
            });
        },

        /**
         * Rebind all events after DOM manipulation (DEPRECATED - using event delegation now)
         * This method is kept for compatibility but no longer needed since we use event delegation
         */
        bindDynamicEvents: function() {
            // No longer needed - event delegation handles all dynamic content automatically
        },

        /**
         * Toggle "How to solve" tips for non-auto-fixable issues
         */
        toggleHowToSolve: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const issueId = $button.data('issue-id');
            const $tip = $('#how-to-solve-' + issueId);
            const $icon = $button.find('.dashicons');

            if ($tip.length === 0) {
                this.showToast(
                    'Help Content Missing',
                    'Help content for this issue could not be found. Please try running a fresh scan.',
                    'warning',
                    5000
                );
                return;
            }

            if ($tip.is(':visible')) {
                // Hide tip
                $tip.slideUp(300);
                $icon.removeClass('dashicons-arrow-up').addClass('dashicons-arrow-down');
                $button.removeClass('expanded');
            } else {
                // Check if content is already loaded (not just loading placeholder)
                const hasRealContent = $tip.find('.loading-content').length === 0;

                if (!hasRealContent) {
                    // Load content via AJAX first
                    this.loadHowToSolveContent(issueId, $tip, $button, $icon);
                } else {
                    // Content already loaded, just show it
                    $tip.slideDown(300);
                    $icon.removeClass('dashicons-arrow-down').addClass('dashicons-arrow-up');
                    $button.addClass('expanded');
                }
            }
        },

        /**
         * Load "How to solve" content via AJAX
         */
        loadHowToSolveContent: function(issueId, $tip, $button, $icon) {
            // Find the issue data from current results
            const issue = this.findIssueById(issueId);
            if (!issue) {
                $tip.html('<p class="error">Issue data not found. Please refresh the page.</p>');
                $tip.slideDown(300);
                return;
            }

            // Show loading state
            $tip.find('.loading-content .dashicons').addClass('spin');
            $tip.slideDown(300);
            $icon.removeClass('dashicons-arrow-down').addClass('dashicons-arrow-up');
            $button.addClass('expanded');

            // Make AJAX request to get dynamic content
            $.ajax({
                url: redcoDiagnosticAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_how_to_solve_tip',
                    nonce: redcoDiagnosticAjax.nonce,
                    issue_data: JSON.stringify(issue)
                },
                success: (response) => {
                    if (response.success) {
                        // Replace loading content with actual help content
                        $tip.html(response.data.content);
                    } else {
                        $tip.html('<p class="error">Failed to load help content: ' + (response.data || 'Unknown error') + '</p>');
                    }
                },
                error: (xhr, status, error) => {
                    $tip.html('<p class="error">Error loading help content. Please try again.</p>');
                }
            });
        },

        /**
         * Find issue by ID in current results
         */
        findIssueById: function(issueId) {
            if (!this.currentResults || !this.currentResults.issues) {
                return null;
            }

            return this.currentResults.issues.find(issue => issue.id === issueId);
        },

        /**
         * Show progress modal
         */
        showProgressModal: function(title) {
            $('#progress-modal-title').text(title);
            $('#diagnostic-progress-modal').show();
            $('#close-progress-modal').hide();

            // Reset progress state
            this.updateProgress(0, 'Initializing...');
            $('#progress-steps').empty();

            // Clear any existing intervals
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }
        },

        /**
         * Hide progress modal
         */
        hideProgressModal: function() {
            // Clear any running progress intervals
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }

            // Hide modal after a brief delay to show completion
            setTimeout(() => {
                $('#diagnostic-progress-modal').hide();
                $('#close-progress-modal').show();

                // Reset progress for next use
                this.updateProgress(0, 'Ready...');
                $('#progress-steps').empty();
            }, 1000);
        },

        /**
         * Update progress
         */
        updateProgress: function(percentage, text) {
            $('.progress-fill').css('width', percentage + '%');
            $('.progress-text').text(text);
        },

        /**
         * Simulate progress updates
         */
        simulateProgress: function(steps) {
            let currentStep = 0;
            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    this.updateProgress(step.progress, step.text);
                    currentStep++;
                } else {
                    clearInterval(interval);
                }
            }, 800);

            // Return the interval ID so it can be cleared externally
            return interval;
        },

        /**
         * Update statistics after fixes
         */
        updateStatistics: function(fixResults) {
            // Update counters based on fix results
            const fixedCount = fixResults.fixes_applied || 0;

            // Update overview stats
            const $issuesFound = $('.overview-stat .stat-value').eq(2);
            const $autoFixable = $('.overview-stat .stat-value').eq(4);
            const $fixesApplied = $('.overview-stat .stat-value').eq(5);

            if ($issuesFound.length) {
                const currentIssues = parseInt($issuesFound.text()) || 0;
                $issuesFound.text(Math.max(0, currentIssues - fixedCount));
            }

            if ($autoFixable.length) {
                const currentAutoFixable = parseInt($autoFixable.text()) || 0;
                $autoFixable.text(Math.max(0, currentAutoFixable - fixedCount));
            }

            if ($fixesApplied.length) {
                const currentFixed = parseInt($fixesApplied.text()) || 0;
                $fixesApplied.text(currentFixed + fixedCount);
            }

            // Update sidebar statistics
            this.updateSidebarStats(fixResults);
        },

        /**
         * Update sidebar statistics
         */
        updateSidebarStats: function(fixResults) {
            // Update auto-fix status if fixes were applied
            if (fixResults.fixes_applied > 0) {
                $('.sidebar-stat').each(function() {
                    const $stat = $(this);
                    if ($stat.find('.stat-label').text().includes('Auto-Fix Status')) {
                        $stat.find('.stat-value').text('Recently Applied');
                    }
                });
            }
        },

        /**
         * Update sidebar statistics after scan completion
         */
        updateSidebarStatsAfterScan: function(scanResults) {

            // Update Last Scan time
            $('.sidebar-stat').each(function() {
                const $stat = $(this);
                const label = $stat.find('.stat-label').text();

                if (label.includes('Last Scan')) {
                    $stat.find('.stat-value').text('Just now');
                } else if (label.includes('Scan Frequency')) {
                    // Keep existing value
                } else if (label.includes('Auto-Fix Status')) {
                    // Update based on auto-fixable issues found
                    if (scanResults.auto_fixable > 0) {
                        $stat.find('.stat-value').text('Available');
                    } else {
                        $stat.find('.stat-value').text('Not Needed');
                    }
                } else if (label.includes('Emergency Mode')) {
                    // Keep existing value unless critical issues found
                    if (scanResults.critical_issues > 3) {
                        $stat.find('.stat-value').text('Recommended');
                    }
                }
            });

            // Update the "Last Scan" info in the overview section
            $('.last-scan-info p').html(`<strong>Last Scan:</strong> Just now`);

        },

        /**
         * Update statistics after single fix
         */
        updateSingleFixStatistics: function() {
            // Count resolved issues
            const resolvedCount = $('.issue-item[data-fix-status="resolved"]').length;
            const failedCount = $('.issue-item[data-fix-status="failed"]').length;

            // Update overview stats - fixes applied counter
            const $fixesApplied = $('.overview-stat .stat-value').eq(5);

            if ($fixesApplied.length) {
                $fixesApplied.text(resolvedCount);
            }

            // Update sidebar statistics
            $('.sidebar-stat').each(function() {
                const $stat = $(this);
                const label = $stat.find('.stat-label').text();

                if (label.includes('Fixes Applied')) {
                    $stat.find('.stat-value').text(resolvedCount);
                } else if (label.includes('Failed Fixes')) {
                    $stat.find('.stat-value').text(failedCount);
                } else if (label.includes('Auto-Fix Status')) {
                    if (resolvedCount > 0) {
                        $stat.find('.stat-value').text('Active');
                    }
                }
            });
        },

        /**
         * Emergency recovery function to handle stuck states
         */
        emergencyRecovery: function() {
 
            // Clear all intervals
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }

            // Hide all modals
            $('#diagnostic-progress-modal').hide();
            $('#close-progress-modal').show();

            // Re-enable all buttons
            this.disableAllButtons(false);

            // Clear all processing states
            $('.issue-item.processing').each(function() {
                const $item = $(this);
                $item.removeClass('processing');
                $item.find('.issue-status-message.processing').remove();
            });

            // Reset progress
            this.updateProgress(0, 'Ready...');
            $('#progress-steps').empty();

            // Show recovery notification
            this.showToast(
                'System Recovered',
                'Emergency recovery completed. You can now try the operation again.',
                'warning',
                8000
            );

        },

        /**
         * Initialize emergency recovery system
         */
        initializeEmergencyRecovery: function() {
            // Bind emergency recovery button
            $('#emergency-recovery-btn').on('click', () => {
                this.emergencyRecovery();
            });

            // Monitor for stuck states and show emergency button
            setInterval(() => {
                const hasProcessingItems = $('.issue-item.processing').length > 0;
                const hasOpenModal = $('#diagnostic-progress-modal').is(':visible');
                const hasDisabledButtons = $('.action-button:disabled').length > 0;

                if (hasProcessingItems || hasOpenModal || hasDisabledButtons) {
                    // Check if it's been stuck for more than 30 seconds
                    if (!this.stuckStateStartTime) {
                        this.stuckStateStartTime = Date.now();
                    } else if (Date.now() - this.stuckStateStartTime > 30000) {
                        // Show emergency recovery button
                        $('#emergency-recovery-btn').show();
                    }
                } else {
                    // Reset stuck state timer and hide button
                    this.stuckStateStartTime = null;
                    $('#emergency-recovery-btn').hide();
                }
            }, 5000); // Check every 5 seconds

        },

        /**
         * Show success notification
         */
        showSuccess: function(message) {
            this.showNotification(message, 'success');
        },

        /**
         * Show error notification
         */
        showError: function(message) {
            this.showNotification(message, 'error');
        },

        /**
         * Show notification - using global toast system
         */
        showNotification: function(message, type) {
            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast(message, type, 5000);
            } else {
                // Fallback to console logging
                console.log(`Diagnostic Autofix ${type}: ${message}`);
            }
        },

        /**
         * Display scan results
         */
        displayScanResults: function(results) {
            // Update overview statistics
            this.updateOverviewStats(results);

            // Update health score circle in header
            if (results.health_score !== undefined) {
                const currentScore = parseInt($('#header-health-score').data('score')) || 0;
                const newScore = parseInt(results.health_score) || 0;
                const trend = newScore - currentScore;
                this.updateHealthScoreCircle(newScore, trend);
            }

            // CRITICAL FIX: Replace the entire scan results section
            this.replaceScanResultsSection(results);

            // Update Apply Auto-Fixes button state
            this.updateAutoFixButtonState(results);
        },

        /**
         * Replace the entire scan results section with fresh data
         */
        replaceScanResultsSection: function(results) {
            // Find the scan results container
            const $scanResultsContainer = $('.redco-content-main');

            if (results.issues && results.issues.length > 0) {
                // Create issues card HTML
                const issuesCardHtml = this.createIssuesCardHtml(results);

                // Find and replace the existing scan results card or no-issues card
                const $existingCard = $scanResultsContainer.find('.redco-card').has('#diagnostic-issues-list, .no-issues-found').first();

                if ($existingCard.length > 0) {
                    $existingCard.replaceWith(issuesCardHtml);
                } else {
                    // If no existing card found, append after the overview section
                    $scanResultsContainer.find('.diagnostic-overview').after(issuesCardHtml);
                }

                // Event delegation handles all dynamic content automatically - no rebinding needed
            } else {
                // Show no issues found state
                this.showNoIssuesState();
            }
        },

        /**
         * Create issues card HTML
         */
        createIssuesCardHtml: function(results) {
            const issues = results.issues || [];
            const displayedIssues = issues.slice(0, 10); // Show first 10 issues

            let issuesHtml = '';
            displayedIssues.forEach((issue, index) => {
                issuesHtml += this.generateIssueHTML(issue, index);
            });

            const showMoreButton = issues.length > 10 ?
                `<div class="show-more-issues">
                    <button type="button" class="button button-link" id="show-all-issues">
                        Show ${issues.length - 10} more issues...
                    </button>
                </div>` : '';

            return `
                <div class="redco-card">
                    <div class="card-header">
                        <h3>
                            <span class="dashicons dashicons-warning"></span>
                            Recent Issues Found
                        </h3>
                        <div class="card-actions">
                            <button type="button" class="button button-secondary" id="run-new-scan">
                                <span class="dashicons dashicons-update"></span>
                                Run New Scan
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="issues-list" id="diagnostic-issues-list">
                            ${issuesHtml}
                            ${showMoreButton}
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Display fix results
         */
        displayFixResults: function(results) {
            let message = `Applied ${results.fixes_applied} fixes successfully!`;
            if (results.fixes_failed > 0) {
                message += ` ${results.fixes_failed} fixes failed.`;
            }
            if (results.backup_created) {
                message += ' Backup created for rollback.';
            }
            this.showSuccess(message);
        },

        /**
         * Update Apply Auto-Fixes button state based on scan results
         */
        updateAutoFixButtonState: function(results) {
            const $autoFixButton = $('#apply-auto-fixes');
            const $buttonBadge = $autoFixButton.find('.button-badge');

            // Count auto-fixable issues
            const autoFixableCount = results.issues ? results.issues.filter(issue => issue.auto_fixable).length : 0;

            if (autoFixableCount > 0) {
                // Enable button and show count
                $autoFixButton.prop('disabled', false);

                if ($buttonBadge.length > 0) {
                    $buttonBadge.text(autoFixableCount);
                } else {
                    $autoFixButton.append(`<span class="button-badge">${autoFixableCount}</span>`);
                }
            } else {
                // Disable button and remove count
                $autoFixButton.prop('disabled', true);
                $buttonBadge.remove();
            }
        },

        /**
         * Update Apply Auto-Fixes button state after single fix
         */
        updateAutoFixButtonStateAfterSingleFix: function() {
            const $autoFixButton = $('#apply-auto-fixes');
            const $buttonBadge = $autoFixButton.find('.button-badge');

            // Count remaining auto-fixable issues that are not resolved or failed
            const remainingAutoFixableCount = $('.issue-item').filter(function() {
                const $item = $(this);
                const hasFixButton = $item.find('.fix-single-issue').length > 0;
                const isResolved = $item.hasClass('resolved') || $item.attr('data-fix-status') === 'resolved';
                const isFailed = $item.hasClass('failed') || $item.attr('data-fix-status') === 'failed';

                return hasFixButton && !isResolved && !isFailed;
            }).length;

            if (remainingAutoFixableCount > 0) {
                // Enable button and update count
                $autoFixButton.prop('disabled', false);

                if ($buttonBadge.length > 0) {
                    $buttonBadge.text(remainingAutoFixableCount);
                } else {
                    $autoFixButton.append(`<span class="button-badge">${remainingAutoFixableCount}</span>`);
                }
            } else {
                // Disable button and remove count
                $autoFixButton.prop('disabled', true);
                $buttonBadge.remove();
            }
        },

        /**
         * Update Apply Auto-Fixes button state after bulk fix
         */
        updateAutoFixButtonStateAfterBulkFix: function() {
            const $autoFixButton = $('#apply-auto-fixes');
            const $buttonBadge = $autoFixButton.find('.button-badge');

            // After bulk fix, all auto-fixable issues should be processed
            // Count any remaining auto-fixable issues that might have failed
            const remainingAutoFixableCount = $('.issue-item').filter(function() {
                const $item = $(this);
                const hasFixButton = $item.find('.fix-single-issue').length > 0;
                const isResolved = $item.hasClass('resolved') || $item.attr('data-fix-status') === 'resolved';
                const isFailed = $item.hasClass('failed') || $item.attr('data-fix-status') === 'failed';

                // Only count items that still have fix buttons and are not resolved
                return hasFixButton && !isResolved && !isFailed;
            }).length;

            if (remainingAutoFixableCount > 0) {
                // Enable button and update count
                $autoFixButton.prop('disabled', false);

                if ($buttonBadge.length > 0) {
                    $buttonBadge.text(remainingAutoFixableCount);
                } else {
                    $autoFixButton.append(`<span class="button-badge">${remainingAutoFixableCount}</span>`);
                }
            } else {
                // Disable button and remove count - all fixes applied
                $autoFixButton.prop('disabled', true);
                $buttonBadge.remove();
            }
        },

        /**
         * Initialize Apply Auto-Fixes button state on page load
         */
        initializeAutoFixButtonState: function() {
            const $autoFixButton = $('#apply-auto-fixes');
            const $buttonBadge = $autoFixButton.find('.button-badge');

            // Count existing auto-fixable issues on page load
            const autoFixableCount = $('.issue-item').filter(function() {
                const $item = $(this);
                return $item.find('.fix-single-issue').length > 0;
            }).length;

            if (autoFixableCount > 0) {
                // Enable button and show count
                $autoFixButton.prop('disabled', false);

                if ($buttonBadge.length > 0) {
                    $buttonBadge.text(autoFixableCount);
                } else {
                    $autoFixButton.append(`<span class="button-badge">${autoFixableCount}</span>`);
                }
            } else {
                // Disable button and remove count
                $autoFixButton.prop('disabled', true);
                $buttonBadge.remove();
            }

        },

        /**
         * Update header health score
         */
        updateHeaderHealthScore: function(results) {
            const $scoreCircle = $('#header-health-score');
            const $scoreValue = $('#header-score-value');
            const $scoreTrend = $('#header-score-trend');
            const $lastUpdated = $('#header-last-updated');

            if (!results || typeof results.health_score === 'undefined') {
                return;
            }

            const newScore = results.health_score || 0;
            const currentScore = parseInt($scoreValue.text()) || 0;
            const scoreDiff = newScore - currentScore;

            // Animate score change
            this.animateScoreChange($scoreValue, currentScore, newScore);

            // Update score circle data attribute
            $scoreCircle.attr('data-score', newScore);

            // Update trend indicator
            if (scoreDiff > 0) {
                $scoreTrend.html('<span class="trend-up">↗ +' + scoreDiff + '</span>');
            } else if (scoreDiff < 0) {
                $scoreTrend.html('<span class="trend-down">↘ ' + scoreDiff + '</span>');
            } else {
                $scoreTrend.html('<span class="trend-neutral">→ 0</span>');
            }

            // Update last updated time
            $lastUpdated.text('Just updated');

            // Add visual feedback
            $scoreCircle.addClass('score-updated');
            setTimeout(() => {
                $scoreCircle.removeClass('score-updated');
            }, 2000);

        },

        /**
         * Animate score change with counting effect
         */
        animateScoreChange: function($element, fromScore, toScore) {
            const duration = 1500; // 1.5 seconds
            const steps = 30;
            const stepDuration = duration / steps;
            const increment = (toScore - fromScore) / steps;
            let currentStep = 0;

            const interval = setInterval(() => {
                currentStep++;
                const currentValue = Math.round(fromScore + (increment * currentStep));
                $element.text(currentValue);

                if (currentStep >= steps) {
                    clearInterval(interval);
                    $element.text(toScore); // Ensure final value is exact
                }
            }, stepDuration);
        },

        /**
         * Update overview statistics
         */
        updateOverviewStats: function(results) {
            // Update header health score first
            this.updateHeaderHealthScore(results);

            // Update stat values with animation
            $('.overview-stat .stat-value').each(function() {
                const $this = $(this);
                const statType = $this.closest('.overview-stat').find('.stat-label').text().toLowerCase();

                if (statType.includes('health')) {
                    $this.text(results.health_score + '%');
                    $this.removeClass('good warning critical').addClass(
                        results.health_score >= 80 ? 'good' :
                        results.health_score >= 60 ? 'warning' : 'critical'
                    );
                } else if (statType.includes('performance')) {
                    $this.text(results.performance_score + '%');
                    $this.removeClass('good warning critical').addClass(
                        results.performance_score >= 80 ? 'good' :
                        results.performance_score >= 60 ? 'warning' : 'critical'
                    );
                } else if (statType.includes('issues found')) {
                    $this.text(results.issues_found);
                } else if (statType.includes('critical')) {
                    $this.text(results.critical_issues);
                } else if (statType.includes('auto-fixable')) {
                    $this.text(results.auto_fixable);
                }
            });
        },



        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showToast('Success', message, 'success', 5000);
        },

        /**
         * Show error message
         */
        showError: function(message) {
            console.error('❌ Error:', message);
            this.showToast('Error', message, 'error', 8000);
        },

        /**
         * Update statistics after single fix
         */
        updateSingleFixStatistics: function() {
            // Update the statistics in the sidebar
            const $resolvedCount = $('.sidebar-stat .stat-value').filter(function() {
                return $(this).siblings('.stat-label').text().includes('Resolved');
            });

            if ($resolvedCount.length) {
                const currentCount = parseInt($resolvedCount.text()) || 0;
                $resolvedCount.text(currentCount + 1);
            }

            // Update issues found count
            const $issuesCount = $('.sidebar-stat .stat-value').filter(function() {
                return $(this).siblings('.stat-label').text().includes('Issues Found');
            });

            if ($issuesCount.length) {
                const currentCount = parseInt($issuesCount.text()) || 0;
                if (currentCount > 0) {
                    $issuesCount.text(currentCount - 1);
                }
            }
        },

        /**
         * Show notice (legacy method for compatibility)
         */
        showNotice: function(message, type) {
            // Convert to toast notification
            const toastType = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
            this.showToast(type.charAt(0).toUpperCase() + type.slice(1), message, toastType, 5000);
        }
    };

    // Make DiagnosticAutoFix globally accessible
    window.DiagnosticAutoFix = DiagnosticAutoFix;

    // Module-specific loading screen removed - now uses universal loading system

    // Initialize the module
    DiagnosticAutoFix.init();

    // Close progress modal handler
    $(document).on('click', '#close-progress-modal', function() {
        $('#diagnostic-progress-modal').hide();
    });
});

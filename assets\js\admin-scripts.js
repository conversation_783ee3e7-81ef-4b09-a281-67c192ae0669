/**
 * Redco Optimizer Admin Scripts - Production Version
 * Clean, optimized JavaScript for WordPress admin interface
 */

(function($) {
    'use strict';

    // Configuration object
    const config = {
        debounceDelay: 500,
        autoSaveDelay: 1500,
        toastDuration: 4000,
        performanceUpdateInterval: redcoAjax.settings ? redcoAjax.settings.performanceUpdateInterval : 30000,
        performanceRetryDelay: 5000
    };

    // Auto-save state management
    let autoSaveTimers = new Map();
    let autoSaveInProgress = new Map();
    let lastSavedData = new Map();

    // Performance monitoring state
    let performanceUpdateTimer = null;
    let performanceUpdateActive = false;

    /**
     * Auto-save functionality for module settings
     */
    function initAutoSave() {
        // Bind auto-save to all form inputs
        $(document).on('input change', 'input, select, textarea', function() {
            const $element = $(this);
            const inputType = $element.attr('type') || $element.prop('tagName').toLowerCase();

            // Skip if element doesn't have proper name attribute
            if (!$element.attr('name')) {
                return;
            }

            // Handle numeric inputs
            if (inputType === 'number') {
                // Auto-save for numeric input
            }

            // Get form context
            const $form = $element.closest('form, .redco-module-form, .redco-optimizer-settings');
            if (!$form.length) return;

            // Determine group and setting name
            const group = $form.data('group') || 'redco_optimizer_settings';
            const name = $element.attr('name');
            let value = $element.val();

            // Handle checkboxes
            if (inputType === 'checkbox') {
                value = $element.is(':checked') ? '1' : '0';
            }

            // Handle radio buttons
            if (inputType === 'radio' && !$element.is(':checked')) {
                return; // Only save when radio is selected
            }

            // Debounce auto-save
            const settingKey = `${group}_${name}`;
            if (autoSaveTimers.has(settingKey)) {
                clearTimeout(autoSaveTimers.get(settingKey));
            }

            const timer = setTimeout(() => {
                performAutoSave(group, name, value, $element[0]);
            }, config.autoSaveDelay);

            autoSaveTimers.set(settingKey, timer);
        });
    }

    /**
     * Perform auto-save for a setting
     */
    function performAutoSave(group, name, value, element) {
        const settingKey = `${group}_${name}`;

        // Check if already in progress
        if (autoSaveInProgress.get(settingKey)) {
            return;
        }

        // Check if value has actually changed
        const lastValue = lastSavedData.get(settingKey);
        if (lastValue === value) {
            return;
        }

        // Check if required variables are available
        if (typeof redco_settings === 'undefined') {
            return;
        }

        if (typeof ajaxurl === 'undefined') {
            return;
        }

        // Auto-save setting

        autoSaveInProgress.set(settingKey, true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                // Process response
                
                if (response.success) {
                    // Show success toast
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Setting saved', 'success');
                    }
                    
                    // Update last saved data
                    lastSavedData.set(settingKey, value);
                } else {
                    // Use global toast notification system for errors
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                    
                    if (typeof showToast === 'function') {
                        showToast(errorMessage, 'error');
                    }
                }
            },
            error: function(xhr, status, error) {
                // Use global toast notification system for network errors
                if (typeof showToast === 'function') {
                    showToast('Network error: Unable to save setting', 'error');
                }
            },
            complete: function() {
                autoSaveInProgress.set(settingKey, false);
            }
        });
    }

    /**
     * Simple toast notification system
     */
    function showToast(message, type = 'info', duration = 3000) {
        // Ensure container exists
        let $container = $('#redco-toast-container');
        if ($container.length === 0) {
            $container = $('<div id="redco-toast-container" class="redco-toast-container"></div>');
            $('body').append($container);
        }

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const $toast = $(`
            <div id="${toastId}" class="redco-toast redco-toast-${type}">
                <div class="redco-toast-icon">${iconMap[type] || iconMap.info}</div>
                <div class="redco-toast-message">${message}</div>
                <button class="redco-toast-close">&times;</button>
            </div>
        `);

        $container.append($toast);

        // Show with animation
        setTimeout(() => {
            $toast.addClass('show');
        }, 100);

        // Auto-hide
        const hideTimeout = setTimeout(() => {
            hideToast(toastId);
        }, duration);

        // Manual close
        $toast.find('.redco-toast-close').on('click', () => {
            clearTimeout(hideTimeout);
            hideToast(toastId);
        });
    }

    /**
     * Hide toast notification
     */
    function hideToast(toastId) {
        const $toast = $('#' + toastId);
        $toast.removeClass('show');
        setTimeout(() => {
            $toast.remove();
        }, 300);
    }

    /**
     * Initialize performance monitoring
     */
    function initPerformanceMonitoring() {
        if (!$('.redco-dashboard').length) return;

        // Start performance updates
        startPerformanceUpdates();
    }

    /**
     * Start performance update timer
     */
    function startPerformanceUpdates() {
        if (performanceUpdateActive) return;

        performanceUpdateActive = true;
        performanceUpdateTimer = setInterval(() => {
            updateAllDashboardSections();
        }, config.performanceUpdateInterval);
    }

    /**
     * Stop performance updates
     */
    function stopPerformanceUpdates() {
        if (performanceUpdateTimer) {
            clearInterval(performanceUpdateTimer);
            performanceUpdateTimer = null;
        }
        performanceUpdateActive = false;
    }

    /**
     * Update all dashboard sections
     */
    function updateAllDashboardSections() {
        updatePerformanceMetrics();
        updateWebsitePerformanceScores();
        updateCoreWebVitalsChart();
    }

    /**
     * Update performance metrics
     */
    function updatePerformanceMetrics() {
        // Implementation for performance metrics update
    }

    /**
     * Update website performance scores
     */
    function updateWebsitePerformanceScores() {
        // Implementation for performance scores update
    }

    /**
     * Update Core Web Vitals chart
     */
    function updateCoreWebVitalsChart() {
        // Implementation for Core Web Vitals chart update
    }

    /**
     * Initialize module page optimizations
     */
    function initModulePageOptimizations() {
        // Only run on module pages
        if (!$('.redco-module-content').length) {
            return;
        }

        initCollapsibleSections();
        initViewModeControls();
        initSectionNavigation();
        initReadingProgress();
        initSmartDefaults();
    }

    /**
     * Initialize collapsible sections
     */
    function initCollapsibleSections() {
        // Make cards collapsible
        $('.redco-card').each(function() {
            const $card = $(this);
            const $header = $card.find('.card-header');

            if ($header.length) {
                $card.addClass('collapsible');

                // Add click handler
                $header.on('click', function(e) {
                    // Don't collapse if clicking on buttons
                    if ($(e.target).is('button, .button, input, select, textarea')) {
                        return;
                    }

                    $card.toggleClass('collapsed');

                    // Save state
                    const cardId = $card.attr('id') || $card.find('h3').text().trim();
                    localStorage.setItem('redco_card_' + cardId, $card.hasClass('collapsed'));
                });

                // Restore state
                const cardId = $card.attr('id') || $card.find('h3').text().trim();
                const isCollapsed = localStorage.getItem('redco_card_' + cardId) === 'true';
                if (isCollapsed) {
                    $card.addClass('collapsed');
                }
            }
        });
    }

    // Make showToast globally available
    window.showToast = showToast;

    // Initialize everything when document is ready
    $(document).ready(function() {
        initAutoSave();
        initPerformanceMonitoring();
        initModulePageOptimizations();
    });

})(jQuery);

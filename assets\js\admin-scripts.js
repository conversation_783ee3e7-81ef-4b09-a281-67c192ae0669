/**
 * Redco Optimizer Admin Scripts - Production Version
 * Clean, optimized JavaScript for WordPress admin interface
 */

(function($) {
    'use strict';

    // Configuration object
    const config = {
        debounceDelay: 500,
        autoSaveDelay: 1500,
        toastDuration: 4000,
        performanceUpdateInterval: redcoAjax.settings ? redcoAjax.settings.performanceUpdateInterval : 30000,
        performanceRetryDelay: 5000
    };

    // Auto-save state management
    let autoSaveTimers = new Map();
    let autoSaveInProgress = new Map();
    let lastSavedData = new Map();

    // Performance monitoring state
    let performanceUpdateTimer = null;
    let performanceUpdateActive = false;

    /**
     * Auto-save functionality for module settings
     */
    function initAutoSave() {
        // Bind auto-save to all form inputs
        $(document).on('input change', 'input, select, textarea', function() {
            const $element = $(this);
            const inputType = $element.attr('type') || $element.prop('tagName').toLowerCase();

            // Skip if element doesn't have proper name attribute
            if (!$element.attr('name')) {
                return;
            }

            // Handle numeric inputs
            if (inputType === 'number') {
                // Auto-save for numeric input
            }

            // Get form context
            const $form = $element.closest('form, .redco-module-form, .redco-optimizer-settings');
            if (!$form.length) return;

            // Determine group and setting name
            const group = $form.data('group') || 'redco_optimizer_settings';
            const name = $element.attr('name');
            let value = $element.val();

            // Handle checkboxes
            if (inputType === 'checkbox') {
                value = $element.is(':checked') ? '1' : '0';
            }

            // Handle radio buttons
            if (inputType === 'radio' && !$element.is(':checked')) {
                return; // Only save when radio is selected
            }

            // Debounce auto-save
            const settingKey = `${group}_${name}`;
            if (autoSaveTimers.has(settingKey)) {
                clearTimeout(autoSaveTimers.get(settingKey));
            }

            const timer = setTimeout(() => {
                performAutoSave(group, name, value, $element[0]);
            }, config.autoSaveDelay);

            autoSaveTimers.set(settingKey, timer);
        });
    }

    /**
     * Perform auto-save for a setting
     */
    function performAutoSave(group, name, value, element) {
        const settingKey = `${group}_${name}`;

        // Check if already in progress
        if (autoSaveInProgress.get(settingKey)) {
            return;
        }

        // Check if value has actually changed
        const lastValue = lastSavedData.get(settingKey);
        if (lastValue === value) {
            return;
        }

        // Check if required variables are available
        if (typeof redco_settings === 'undefined') {
            return;
        }

        if (typeof ajaxurl === 'undefined') {
            return;
        }

        // Auto-save setting

        autoSaveInProgress.set(settingKey, true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                // Process response
                
                if (response.success) {
                    // Show success toast
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Setting saved', 'success');
                    }
                    
                    // Update last saved data
                    lastSavedData.set(settingKey, value);
                } else {
                    // Use global toast notification system for errors
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                    
                    if (typeof showToast === 'function') {
                        showToast(errorMessage, 'error');
                    }
                }
            },
            error: function(xhr, status, error) {
                // Use global toast notification system for network errors
                if (typeof showToast === 'function') {
                    showToast('Network error: Unable to save setting', 'error');
                }
            },
            complete: function() {
                autoSaveInProgress.set(settingKey, false);
            }
        });
    }

    /**
     * Simple toast notification system
     */
    function showToast(message, type = 'info', duration = 3000) {
        // Ensure container exists
        let $container = $('#redco-toast-container');
        if ($container.length === 0) {
            $container = $('<div id="redco-toast-container" class="redco-toast-container"></div>');
            $('body').append($container);
        }

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const $toast = $(`
            <div id="${toastId}" class="redco-toast redco-toast-${type}">
                <div class="redco-toast-icon">${iconMap[type] || iconMap.info}</div>
                <div class="redco-toast-message">${message}</div>
                <button class="redco-toast-close">&times;</button>
            </div>
        `);

        $container.append($toast);

        // Show with animation
        setTimeout(() => {
            $toast.addClass('show');
        }, 100);

        // Auto-hide
        const hideTimeout = setTimeout(() => {
            hideToast(toastId);
        }, duration);

        // Manual close
        $toast.find('.redco-toast-close').on('click', () => {
            clearTimeout(hideTimeout);
            hideToast(toastId);
        });
    }

    /**
     * Hide toast notification
     */
    function hideToast(toastId) {
        const $toast = $('#' + toastId);
        $toast.removeClass('show');
        setTimeout(() => {
            $toast.remove();
        }, 300);
    }

    /**
     * Initialize performance monitoring
     */
    function initPerformanceMonitoring() {
        if (!$('.redco-dashboard').length) return;

        // Start performance updates
        startPerformanceUpdates();
    }

    /**
     * Start performance update timer
     */
    function startPerformanceUpdates() {
        if (performanceUpdateActive) return;

        performanceUpdateActive = true;
        performanceUpdateTimer = setInterval(() => {
            updateAllDashboardSections();
        }, config.performanceUpdateInterval);
    }

    /**
     * Stop performance updates
     */
    function stopPerformanceUpdates() {
        if (performanceUpdateTimer) {
            clearInterval(performanceUpdateTimer);
            performanceUpdateTimer = null;
        }
        performanceUpdateActive = false;
    }

    /**
     * Update all dashboard sections
     */
    function updateAllDashboardSections() {
        updatePerformanceMetrics();
        updateWebsitePerformanceScores();
        updateCoreWebVitalsChart();
    }

    /**
     * Update performance metrics
     */
    function updatePerformanceMetrics() {
        // Implementation for performance metrics update
    }

    /**
     * Update website performance scores
     */
    function updateWebsitePerformanceScores() {
        // Implementation for performance scores update
    }

    /**
     * Update Core Web Vitals chart
     */
    function updateCoreWebVitalsChart() {
        // Implementation for Core Web Vitals chart update
    }

    /**
     * Initialize module page optimizations
     */
    function initModulePageOptimizations() {
        // Only run on module pages
        if (!$('.redco-module-content').length) {
            return;
        }

        initCollapsibleSections();
        initViewModeControls();
        initSectionNavigation();
        initReadingProgress();
        initSmartDefaults();
    }

    /**
     * Initialize collapsible sections
     */
    function initCollapsibleSections() {
        // Make cards collapsible
        $('.redco-card').each(function() {
            const $card = $(this);
            const $header = $card.find('.card-header');

            if ($header.length) {
                $card.addClass('collapsible');

                // Add click handler
                $header.on('click', function(e) {
                    // Don't collapse if clicking on buttons
                    if ($(e.target).is('button, .button, input, select, textarea')) {
                        return;
                    }

                    $card.toggleClass('collapsed');

                    // Save state
                    const cardId = $card.attr('id') || $card.find('h3').text().trim();
                    localStorage.setItem('redco_card_' + cardId, $card.hasClass('collapsed'));
                });

                // Restore state
                const cardId = $card.attr('id') || $card.find('h3').text().trim();
                const isCollapsed = localStorage.getItem('redco_card_' + cardId) === 'true';
                if (isCollapsed) {
                    $card.addClass('collapsed');
                }
            }
        });
    }

    /**
     * Initialize view mode controls
     */
    function initViewModeControls() {
        // Create view mode controls for better UX
        const $controls = $(`
            <div class="view-mode-controls">
                <button class="view-mode-btn" data-mode="normal" title="Normal View">
                    <span class="dashicons dashicons-list-view"></span>
                </button>
                <button class="view-mode-btn" data-mode="compact" title="Compact View">
                    <span class="dashicons dashicons-grid-view"></span>
                </button>
            </div>
        `);

        // Only add if we're on a module page
        if ($('.redco-module-content').length) {
            $('.redco-module-content').prepend($controls);

            // Handle view mode changes
            $('.view-mode-btn').on('click', function() {
                const mode = $(this).data('mode');
                const $content = $('.redco-module-content');

                $('.view-mode-btn').removeClass('active');
                $(this).addClass('active');

                if (mode === 'compact') {
                    $content.addClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'compact');
                } else {
                    $content.removeClass('compact-mode');
                    localStorage.setItem('redco_view_mode', 'normal');
                }
            });

            // Restore view mode
            const savedMode = localStorage.getItem('redco_view_mode');
            if (savedMode === 'compact') {
                $('.redco-module-content').addClass('compact-mode');
                $('.view-mode-btn[data-mode="compact"]').addClass('active');
            } else {
                $('.view-mode-btn[data-mode="normal"]').addClass('active');
            }
        }
    }

    /**
     * Initialize section navigation
     */
    function initSectionNavigation() {
        const $cards = $('.redco-card');
        if ($cards.length < 3) return; // Don't show nav for short pages

        // Create navigation
        const $nav = $('<div class="section-navigation"></div>');
        const $navTitle = $('<div class="section-nav-title">Quick Navigation</div>');
        const $navLinks = $('<div class="section-nav-links"></div>');

        $cards.each(function(index) {
            const $card = $(this);
            const title = $card.find('h3').text().trim();
            if (title) {
                const id = 'section-' + index;
                $card.attr('id', id);

                const $link = $(`<a href="#${id}" class="section-nav-link">${title}</a>`);
                $navLinks.append($link);
            }
        });

        if ($navLinks.children().length > 0) {
            $nav.append($navTitle, $navLinks);
            $('.redco-module-content').prepend($nav);

            // Handle navigation clicks
            $('.section-nav-link').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('href');
                const $target = $(target);

                if ($target.length) {
                    // Expand section if collapsed
                    $target.removeClass('collapsed');

                    // Smooth scroll
                    $('html, body').animate({
                        scrollTop: $target.offset().top - 100
                    }, 500);

                    // Update active state
                    $('.section-nav-link').removeClass('active');
                    $(this).addClass('active');
                }
            });
        }
    }

    /**
     * Initialize reading progress indicator
     */
    function initReadingProgress() {
        // Only add progress bar on longer pages
        if ($(document).height() < $(window).height() * 2) return;

        // Create progress bar
        const $progressBar = $(`
            <div class="reading-progress">
                <div class="reading-progress-bar"></div>
            </div>
        `);

        $('body').append($progressBar);

        // Update progress on scroll
        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const docHeight = $(document).height() - $(window).height();
            const progress = Math.min((scrollTop / docHeight) * 100, 100);

            $('.reading-progress-bar').css('width', progress + '%');
        });
    }

    /**
     * Initialize smart defaults
     */
    function initSmartDefaults() {
        // Set smart defaults based on user behavior
        const $forms = $('.redco-module-form');

        $forms.each(function() {
            const $form = $(this);
            const module = $form.data('module');

            // Apply smart defaults based on module type
            switch(module) {
                case 'page-cache':
                    setPageCacheDefaults($form);
                    break;
                case 'database-cleanup':
                    setDatabaseCleanupDefaults($form);
                    break;
                case 'critical-resource-optimizer':
                    setCriticalResourceDefaults($form);
                    break;
            }
        });
    }

    /**
     * Set smart defaults for page cache
     */
    function setPageCacheDefaults($form) {
        // Set recommended defaults for page cache
        const defaults = {
            'expiration': '3600',
            'compression': '1',
            'mobile_cache': '1'
        };

        Object.keys(defaults).forEach(setting => {
            const $field = $form.find(`[name="${setting}"]`);
            if ($field.length && !$field.val()) {
                $field.val(defaults[setting]);
            }
        });
    }

    /**
     * Set smart defaults for database cleanup
     */
    function setDatabaseCleanupDefaults($form) {
        // Set recommended defaults for database cleanup
        const defaults = {
            'cleanup_revisions': '1',
            'cleanup_auto_drafts': '1',
            'cleanup_trashed_posts': '1',
            'cleanup_spam_comments': '1',
            'cleanup_expired_transients': '1'
        };

        Object.keys(defaults).forEach(setting => {
            const $field = $form.find(`[name="${setting}"]`);
            if ($field.length && !$field.is(':checked')) {
                $field.prop('checked', true);
            }
        });
    }

    /**
     * Set smart defaults for critical resource optimizer
     */
    function setCriticalResourceDefaults($form) {
        // Set recommended defaults for critical resource optimizer
        const defaults = {
            'enable_critical_css': '1',
            'preload_key_resources': '1',
            'optimize_fonts': '1'
        };

        Object.keys(defaults).forEach(setting => {
            const $field = $form.find(`[name="${setting}"]`);
            if ($field.length && !$field.is(':checked')) {
                $field.prop('checked', true);
            }
        });
    }

    // Make showToast globally available
    window.showToast = showToast;

    // Initialize everything when document is ready
    $(document).ready(function() {
        initAutoSave();
        initPerformanceMonitoring();
        initModulePageOptimizations();
    });

})(jQuery);

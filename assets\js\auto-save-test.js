/**
 * Auto-Save Test Script for Redco Optimizer
 * Run this in browser console to test auto-save functionality
 */

(function() {
    'use strict';

    console.log('🔧 Redco Auto-Save Test Script Loaded');

    // Test function to verify auto-save functionality
    window.testRedcoAutoSave = function() {
        console.log('🧪 Starting Redco Auto-Save Tests...');

        // Test 1: Check if required objects are available
        console.log('\n📋 Test 1: Checking Required Objects');
        console.log('- redco_settings:', typeof redco_settings !== 'undefined' ? '✅ Available' : '❌ Missing');
        if (typeof redco_settings !== 'undefined') {
            console.log('  - nonce:', redco_settings.nonce ? '✅ Present' : '❌ Missing');
            console.log('  - ajaxurl:', redco_settings.ajaxurl ? '✅ Present' : '❌ Missing');
        }
        console.log('- ajaxurl:', typeof ajaxurl !== 'undefined' ? '✅ Available' : '❌ Missing');
        console.log('- showToast:', typeof showToast === 'function' ? '✅ Available' : '❌ Missing');
        console.log('- jQuery:', typeof jQuery !== 'undefined' ? '✅ Available' : '❌ Missing');

        // Test 2: Find form fields
        console.log('\n📋 Test 2: Finding Form Fields');
        const $forms = jQuery('.redco-settings-form');
        console.log('- Settings forms found:', $forms.length);

        const $inputs = jQuery('.redco-settings-form input');
        const $selects = jQuery('.redco-settings-form select');
        const $textareas = jQuery('.redco-settings-form textarea');
        
        console.log('- Input fields found:', $inputs.length);
        console.log('- Select fields found:', $selects.length);
        console.log('- Textarea fields found:', $textareas.length);

        // Test 3: Check specific field types
        console.log('\n📋 Test 3: Checking Specific Field Types');
        const $numberInputs = jQuery('input[type="number"]');
        const $textInputs = jQuery('input[type="text"]');
        const $checkboxes = jQuery('input[type="checkbox"]');
        
        console.log('- Number inputs:', $numberInputs.length);
        console.log('- Text inputs:', $textInputs.length);
        console.log('- Checkboxes:', $checkboxes.length);

        // Test 4: Check field names
        console.log('\n📋 Test 4: Checking Field Names');
        $numberInputs.each(function(index) {
            const $field = jQuery(this);
            console.log(`- Number input ${index + 1}:`, {
                name: $field.attr('name'),
                value: $field.val(),
                classes: $field.attr('class')
            });
        });

        // Test 5: Manual auto-save test
        console.log('\n📋 Test 5: Manual Auto-Save Test');
        if ($numberInputs.length > 0) {
            const $testField = $numberInputs.first();
            const originalValue = $testField.val();
            const testValue = parseFloat(originalValue) + 0.01;
            
            console.log('Testing with field:', $testField.attr('name'));
            console.log('Original value:', originalValue);
            console.log('Test value:', testValue);
            
            // Trigger change event
            $testField.val(testValue).trigger('input').trigger('change');
            console.log('✅ Change event triggered - check for auto-save in console');
            
            // Restore original value after 5 seconds
            setTimeout(function() {
                $testField.val(originalValue).trigger('input').trigger('change');
                console.log('✅ Original value restored');
            }, 5000);
        } else {
            console.log('❌ No number inputs found for testing');
        }

        console.log('\n🎯 Test Complete! Check console for auto-save activity.');
    };

    // Test function to manually trigger auto-save
    window.testManualAutoSave = function(group, name, value) {
        console.log('🔧 Manual Auto-Save Test:', { group, name, value });

        if (typeof redco_settings === 'undefined') {
            console.error('❌ redco_settings not available');
            return;
        }

        jQuery.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                console.log('✅ Manual auto-save success:', response);
                if (typeof showToast === 'function') {
                    showToast('Manual test: ' + (response.data.message || 'Setting saved'), 'success');
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Manual auto-save error:', { xhr, status, error });
                if (typeof showToast === 'function') {
                    showToast('Manual test failed: ' + error, 'error');
                }
            }
        });
    };

    // Quick test function for numeric inputs specifically
    window.testNumericAutoSave = function() {
        console.log('🧪 Testing Numeric Auto-Save...');

        // Test the slow query threshold field specifically
        const testValue = (Math.random() * 0.9 + 0.1).toFixed(2); // Random value between 0.1 and 1.0
        console.log('Testing with value:', testValue);

        testManualAutoSave('redco_optimizer_performance', 'slow_query_threshold', testValue);

        // Also test memory threshold
        setTimeout(function() {
            const memoryValue = Math.floor(Math.random() * 1000000) + 67108864; // Random value around 64MB
            console.log('Testing memory threshold with value:', memoryValue);
            testManualAutoSave('redco_optimizer_performance', 'memory_threshold', memoryValue);
        }, 2000);
    };

    // Auto-run basic tests when script loads
    if (jQuery && jQuery('.redco-settings-form').length > 0) {
        console.log('🎯 Redco settings page detected. Run testRedcoAutoSave() to test functionality.');
    }

})();

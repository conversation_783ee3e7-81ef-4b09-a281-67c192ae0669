/**
 * Clean Enterprise Settings JavaScript
 * Redco Optimizer Plugin
 * Built from scratch for enterprise-grade functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Auto-save configuration
    const SAVE_DELAY = 1000; // 1 second delay
    let saveTimeout;

    /**
     * Initialize settings functionality
     */
    function initSettings() {
        initAutoSave();
        initFormControls();
    }

    /**
     * Initialize comprehensive auto-save functionality for ALL form field types
     */
    function initAutoSave() {
        // COMPREHENSIVE AUTO-SAVE: Bind to ALL form field types
        $(document).on('change input', '.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea', function() {
            const $element = $(this);
            const elementType = $element.prop('tagName').toLowerCase();
            const inputType = $element.attr('type');

            // Skip if element doesn't have proper name attribute
            if (!$element.attr('name')) {
                return;
            }

            // Extract setting group and name from name attribute
            let settingGroup, settingName, value;

            // Handle different name formats
            const nameAttr = $element.attr('name');
            if (nameAttr.includes('[') && nameAttr.includes(']')) {
                // Format: group[setting] or group[setting][sub]
                const matches = nameAttr.match(/^([^[]+)\[([^\]]+)\](?:\[([^\]]+)\])?$/);
                if (matches) {
                    settingGroup = matches[1];
                    settingName = matches[2];

                    // Handle sub-settings (like arrays)
                    if (matches[3]) {
                        settingName = matches[2] + '[' + matches[3] + ']';
                    }
                }
            } else if ($element.data('setting-group') && $element.data('setting-name')) {
                // Use data attributes if available
                settingGroup = $element.data('setting-group');
                settingName = $element.data('setting-name');
            } else {
                // Skip if we can't determine the setting structure
                return;
            }

            // Get value based on element type
            if (inputType === 'checkbox') {
                value = $element.is(':checked') ? 1 : 0;
            } else if (inputType === 'radio') {
                // Only save if this radio button is checked
                if (!$element.is(':checked')) {
                    return;
                }
                value = $element.val();
            } else if (elementType === 'select') {
                value = $element.val();
            } else if (elementType === 'textarea') {
                value = $element.val();
            } else {
                // text, number, email, url, etc.
                value = $element.val();
            }

            // Clear existing timeout
            clearTimeout(saveTimeout);

            // Add visual feedback
            $element.closest('.setting-item').addClass('saving');

            // Save after delay
            saveTimeout = setTimeout(function() {
                autoSaveSetting(settingGroup, settingName, value, $element);
            }, SAVE_DELAY);
        });
    }

    /**
     * Auto-save a setting
     */
    function autoSaveSetting(settingGroup, settingName, value, $element) {
        const $settingItem = $element.closest('.setting-item');

        // Add saving state
        $settingItem.addClass('saving').removeClass('saved error');

        // Prepare data
        const data = {
            action: 'redco_auto_save_setting',
            group: settingGroup,
            name: settingName,
            value: value,
            nonce: redco_settings.nonce
        };

        // Make AJAX request
        $.ajax({
            url: redco_settings.ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                // Remove saving indicator
                $settingItem.removeClass('saving');

                if (response.success) {
                    // Use global toast notification system
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Setting saved successfully', 'success', 2000);
                    } else if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.success(response.data.message || 'Setting saved successfully', {
                            title: 'Auto-saved',
                            duration: 2000
                        });
                    } else {
                        // Fallback: Add temporary saved class for visual feedback
                        $settingItem.addClass('saved');
                        setTimeout(function() {
                            $settingItem.removeClass('saved');
                        }, 2000);
                    }
                } else {
                    // Use global toast notification system for errors
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                    if (typeof showToast === 'function') {
                        showToast(errorMessage, 'error', 4000);
                    } else if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.error(errorMessage, {
                            title: 'Save Failed',
                            duration: 4000
                        });
                    } else {
                        // Fallback: Add temporary error class for visual feedback
                        $settingItem.addClass('error');
                        setTimeout(function() {
                            $settingItem.removeClass('error');
                        }, 3000);
                    }
                }
            },
            error: function(xhr, status, error) {
                // Remove saving indicator
                $settingItem.removeClass('saving');

                // Use global toast notification system for AJAX errors
                const errorMessage = 'Network error: Unable to save setting';
                if (typeof showToast === 'function') {
                    showToast(errorMessage, 'error', 4000);
                } else if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.error(errorMessage, {
                        title: 'Connection Error',
                        duration: 4000
                    });
                } else {
                    // Fallback: Add temporary error class for visual feedback
                    $settingItem.addClass('error');
                    setTimeout(function() {
                        $settingItem.removeClass('error');
                    }, 3000);
                }
            }
        });
    }

    /**
     * Initialize form controls
     */
    function initFormControls() {
        // Prevent form submission since we auto-save
        $('.redco-settings-form').on('submit', function(e) {
            e.preventDefault();
            return false;
        });

        // Hide any save buttons
        $('.redco-save-button, .settings-form-footer').hide();
    }

    /**
     * Initialize everything
     */
    initSettings();
});

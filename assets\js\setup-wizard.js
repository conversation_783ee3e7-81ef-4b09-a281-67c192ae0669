/**
 * Setup Wizard JavaScript
 */
(function($) {
    'use strict';

    var SetupWizard = {

        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // Setup option selection
            $(document).on('click', '.setup-option', this.selectSetupOption);

            // Setup button click
            $(document).on('click', '.wizard-btn-setup', this.completeSetup);

            // Skip button click
            $(document).on('click', '.wizard-btn-skip', this.skipSetup);

            // Rollback button click
            $(document).on('click', '.wizard-btn-rollback', this.rollbackSetup);
        },

        selectSetupOption: function(e) {
            e.preventDefault();

            var $option = $(this);
            var setupType = $option.data('setup-type');

            // Remove selected class from all options
            $('.setup-option').removeClass('selected');

            // Add selected class to clicked option
            $option.addClass('selected');

            // Store selected setup type
            $('#selected-setup-type').val(setupType);

            // Enable setup button
            $('.wizard-btn-setup').prop('disabled', false);
        },

        completeSetup: function(e) {
            e.preventDefault();

            var setupType = $('#selected-setup-type').val();

            if (!setupType) {
                alert(redcoWizard.strings.selectSetupType);
                return;
            }

            var $button = $(this);

            // Show loading state
            $button.addClass('loading').prop('disabled', true);
            $button.find('.dashicons').removeClass('dashicons-arrow-right-alt2').addClass('dashicons-update');

            // Perform AJAX request
            $.ajax({
                url: redcoWizard.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_complete_setup',
                    setup_type: setupType,
                    nonce: redcoWizard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        SetupWizard.showMessage(response.data.message, 'success');

                        // Redirect after a short delay
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    } else {
                        SetupWizard.showMessage(response.data || redcoWizard.strings.error, 'error');
                        SetupWizard.resetButton($button);
                    }
                },
                error: function() {
                    SetupWizard.showMessage(redcoWizard.strings.error, 'error');
                    SetupWizard.resetButton($button);
                }
            });
        },

        skipSetup: function(e) {
            e.preventDefault();

            // No confirmation needed - provide immediate feedback


            var $button = $(this);

            // Show loading state
            $button.text(redcoWizard.strings.saving);

            // Perform AJAX request
            $.ajax({
                url: redcoWizard.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_skip_setup',
                    nonce: redcoWizard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        SetupWizard.showMessage(response.data.message, 'success');

                        // Redirect after a short delay
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    } else {
                        SetupWizard.showMessage(response.data || redcoWizard.strings.error, 'error');
                        $button.text(redcoWizard.strings.skipSetup);
                    }
                },
                error: function() {
                    SetupWizard.showMessage(redcoWizard.strings.error, 'error');
                    $button.text(redcoWizard.strings.skipSetup);
                }
            });
        },

        rollbackSetup: function(e) {
            e.preventDefault();

            if (!confirm('Are you sure you want to rollback to your previous configuration? This will undo all changes made by the setup wizard.')) {
                return;
            }

            var $button = $(this);

            // Show loading state
            $button.addClass('loading').prop('disabled', true);
            $button.find('.dashicons').removeClass('dashicons-undo').addClass('dashicons-update');

            // Perform AJAX request
            $.ajax({
                url: redcoWizard.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_rollback_setup',
                    nonce: redcoWizard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        SetupWizard.showMessage(response.data.message, 'success');

                        // Redirect to dashboard after a short delay
                        setTimeout(function() {
                            window.location.href = redcoWizard.adminUrl + 'admin.php?page=redco-optimizer';
                        }, 2000);
                    } else {
                        SetupWizard.showMessage(response.data || redcoWizard.strings.error, 'error');
                        SetupWizard.resetRollbackButton($button);
                    }
                },
                error: function() {
                    SetupWizard.showMessage(redcoWizard.strings.error, 'error');
                    SetupWizard.resetRollbackButton($button);
                }
            });
        },

        resetButton: function($button) {
            $button.removeClass('loading').prop('disabled', false);
            $button.find('.dashicons').removeClass('dashicons-update').addClass('dashicons-arrow-right-alt2');
        },

        resetRollbackButton: function($button) {
            $button.removeClass('loading').prop('disabled', false);
            $button.find('.dashicons').removeClass('dashicons-update').addClass('dashicons-undo');
        },

        showMessage: function(message, type) {
            // Use global toast notification system
            if (typeof showToast === 'function') {
                const duration = type === 'success' ? 3000 : 5000;
                showToast(message, type, duration);
            } else {
                // Fallback for legacy support
                console.log('Setup Wizard ' + type + ': ' + message);
            }
        },

        animateProgress: function(step, totalSteps) {
            var percentage = (step / totalSteps) * 100;
            $('.progress-fill').css('width', percentage + '%');

            // Update step indicators
            $('.step-indicator').each(function(index) {
                var $indicator = $(this);
                var stepNumber = index + 1;

                if (stepNumber < step) {
                    $indicator.removeClass('active pending').addClass('completed');
                    $indicator.html('<span class="dashicons dashicons-yes"></span>');
                } else if (stepNumber === step) {
                    $indicator.removeClass('completed pending').addClass('active');
                    $indicator.html('<span>' + stepNumber + '</span>');
                } else {
                    $indicator.removeClass('active completed').addClass('pending');
                    $indicator.html('<span>' + stepNumber + '</span>');
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SetupWizard.init();

        // Animate progress bar on page load
        var currentStep = parseInt($('.wizard-progress').data('current-step')) || 1;
        var totalSteps = parseInt($('.wizard-progress').data('total-steps')) || 3;

        setTimeout(function() {
            SetupWizard.animateProgress(currentStep, totalSteps);
        }, 500);
    });

})(jQuery);

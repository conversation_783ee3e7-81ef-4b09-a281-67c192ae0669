/**
 * Redco Optimizer Settings Auto-Save
 * Minimal and clean auto-save functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    // Auto-save functionality
    let saveTimeout;
    const SAVE_DELAY = 1000; // 1 second delay

    // COMPREHENSIVE AUTO-SAVE: Bind to ALL form field types
    $(document).on('change input', '.redco-settings-form input, .redco-settings-form select, .redco-settings-form textarea', function() {
        const $element = $(this);
        const elementType = $element.prop('tagName').toLowerCase();
        const inputType = $element.attr('type');

        // Skip if element doesn't have proper name attribute
        if (!$element.attr('name')) {
            return;
        }

        // Extract setting group and name from name attribute
        let settingGroup, settingName, value;

        // Handle different name formats
        const nameAttr = $element.attr('name');
        if (nameAttr.includes('[') && nameAttr.includes(']')) {
            // Format: group[setting] or group[setting][sub]
            const matches = nameAttr.match(/^([^[]+)\[([^\]]+)\](?:\[([^\]]+)\])?$/);
            if (matches) {
                settingGroup = matches[1];
                settingName = matches[2];

                // Handle sub-settings (like arrays)
                if (matches[3]) {
                    settingName = matches[2] + '[' + matches[3] + ']';
                }
            }
        } else if ($element.data('setting-group') && $element.data('setting-name')) {
            // Use data attributes if available
            settingGroup = $element.data('setting-group');
            settingName = $element.data('setting-name');
        } else {
            // Skip if we can't determine the setting structure
            return;
        }

        // Get value based on element type
        if (inputType === 'checkbox') {
            value = $element.is(':checked') ? 1 : 0;
        } else if (inputType === 'radio') {
            // Only save if this radio button is checked
            if (!$element.is(':checked')) {
                return;
            }
            value = $element.val();
        } else if (elementType === 'select') {
            value = $element.val();
        } else if (elementType === 'textarea') {
            value = $element.val();
        } else {
            // text, number, email, url, etc.
            value = $element.val();
        }

        // Clear existing timeout
        clearTimeout(saveTimeout);

        // Add visual feedback
        $element.closest('.setting-item').addClass('saving');

        // Save after delay
        saveTimeout = setTimeout(function() {
            autoSaveSetting(settingGroup, settingName, value, $element);
        }, SAVE_DELAY);
    });

    // Auto-save function
    function autoSaveSetting(group, name, value, $element) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_auto_save_setting',
                nonce: redco_settings.nonce,
                group: group,
                name: name,
                value: value
            },
            success: function(response) {
                // Remove saving indicator
                $element.closest('.setting-item').removeClass('saving');

                if (response.success) {
                    // Use global toast notification system
                    if (typeof showToast === 'function') {
                        showToast(response.data.message || 'Setting saved successfully', 'success', 2000);
                    } else if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.success(response.data.message || 'Setting saved successfully', {
                            title: 'Auto-saved',
                            duration: 2000
                        });
                    } else {
                        // Fallback: Add temporary saved class for visual feedback
                        $element.closest('.setting-item').addClass('saved');
                        setTimeout(function() {
                            $element.closest('.setting-item').removeClass('saved');
                        }, 2000);
                    }
                } else {
                    // Use global toast notification system for errors
                    const errorMessage = response.data && response.data.message ? response.data.message : 'Failed to save setting';
                    if (typeof showToast === 'function') {
                        showToast(errorMessage, 'error', 4000);
                    } else if (typeof RedcoToast !== 'undefined') {
                        RedcoToast.error(errorMessage, {
                            title: 'Save Failed',
                            duration: 4000
                        });
                    } else {
                        // Fallback: Add temporary error class for visual feedback
                        $element.closest('.setting-item').addClass('error');
                        setTimeout(function() {
                            $element.closest('.setting-item').removeClass('error');
                        }, 3000);
                    }
                }
            },
            error: function(xhr, status, error) {
                // Remove saving indicator
                $element.closest('.setting-item').removeClass('saving');

                // Use global toast notification system for AJAX errors
                const errorMessage = 'Network error: Unable to save setting';
                if (typeof showToast === 'function') {
                    showToast(errorMessage, 'error', 4000);
                } else if (typeof RedcoToast !== 'undefined') {
                    RedcoToast.error(errorMessage, {
                        title: 'Connection Error',
                        duration: 4000
                    });
                } else {
                    // Fallback: Add temporary error class for visual feedback
                    $element.closest('.setting-item').addClass('error');
                    setTimeout(function() {
                        $element.closest('.setting-item').removeClass('error');
                    }, 3000);
                }
            }
        });
    }

    // Settings page uses server-side navigation, not client-side tabs
    // Remove the tab switching JavaScript that was preventing normal navigation
});

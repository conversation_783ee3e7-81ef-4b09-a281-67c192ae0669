<!DOCTYPE html>
<html>
<head>
    <title>Redco Auto-Save Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .test-field { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { padding: 8px; margin-bottom: 10px; }
        .debug-output { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔧 Redco Auto-Save Debug Test</h1>
    <p>This page helps debug auto-save functionality. Open browser console to see detailed logs.</p>

    <div class="test-section">
        <h2>📋 Test Instructions</h2>
        <ol>
            <li>Open browser Developer Tools (F12)</li>
            <li>Go to Console tab</li>
            <li>Navigate to Redco Optimizer Settings page</li>
            <li>Run: <code>testRedcoAutoSave()</code></li>
            <li>Test each numeric input field individually</li>
            <li>Check console for detailed debug output</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🧪 Manual AJAX Test</h2>
        <p>Run this in browser console on the settings page:</p>
        <div class="debug-output">
// Test numeric input auto-save
testManualAutoSave('redco_optimizer_performance', 'slow_query_threshold', '0.5');

// Test text input auto-save  
testManualAutoSave('redco_optimizer_performance', 'api_key', 'test-key-123');

// Test select dropdown auto-save
testManualAutoSave('redco_optimizer_performance', 'cache_duration', '300');
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Debug Checklist</h2>
        <div class="test-field">
            <label>✅ Check if redco_settings object exists:</label>
            <div class="debug-output">console.log('redco_settings:', typeof redco_settings !== 'undefined' ? redco_settings : 'MISSING');</div>
        </div>
        
        <div class="test-field">
            <label>✅ Check if showToast function exists:</label>
            <div class="debug-output">console.log('showToast:', typeof showToast === 'function' ? 'AVAILABLE' : 'MISSING');</div>
        </div>
        
        <div class="test-field">
            <label>✅ Check if form fields are found:</label>
            <div class="debug-output">console.log('Form fields:', jQuery('.redco-settings-form input').length);</div>
        </div>
        
        <div class="test-field">
            <label>✅ Check numeric inputs specifically:</label>
            <div class="debug-output">console.log('Numeric inputs:', jQuery('input[type="number"]').length);</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🚨 Common Issues & Solutions</h2>
        
        <div class="test-field">
            <h3 class="error">❌ "redco_settings object not found"</h3>
            <p><strong>Solution:</strong> The JavaScript localization is not working. Check if the settings page is loading the correct JS files.</p>
        </div>
        
        <div class="test-field">
            <h3 class="error">❌ "showToast function missing"</h3>
            <p><strong>Solution:</strong> The admin-scripts.js file is not loaded. Check the loader configuration.</p>
        </div>
        
        <div class="test-field">
            <h3 class="error">❌ "update_option returned false"</h3>
            <p><strong>Solution:</strong> Database permission issue or option name mismatch. Check debug logs.</p>
        </div>
        
        <div class="test-field">
            <h3 class="error">❌ "No form fields found"</h3>
            <p><strong>Solution:</strong> CSS selector issue. Check if .redco-settings-form class exists on the form.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Expected Debug Output</h2>
        <div class="debug-output">
<span class="info">// When changing a numeric input, you should see:</span>
<span class="success">Redco Auto-Save: Numeric input detected {
    element: input.redco-number-input,
    name: "redco_optimizer_performance[slow_query_threshold]", 
    value: "0.2",
    classes: "redco-number-input"
}</span>

<span class="success">Redco Auto-Save: {
    group: "redco_optimizer_performance",
    name: "slow_query_threshold",
    value: "0.2",
    nonce: "abc123...",
    ajaxurl: "/wp-admin/admin-ajax.php"
}</span>

<span class="success">Redco Auto-Save Response: {
    success: true,
    data: {
        message: "Setting saved successfully",
        group: "redco_optimizer_performance", 
        name: "slow_query_threshold",
        value: 0.2,
        saved_value: 0.2,
        value_type: "double",
        saved_type: "double"
    }
}</span>

<span class="success">Redco Auto-Save: Toast notification shown via showToast</span>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Success Criteria</h2>
        <ul>
            <li>✅ Console shows numeric input detection logs</li>
            <li>✅ AJAX requests are sent with correct data</li>
            <li>✅ Server responds with success: true</li>
            <li>✅ Green toast notifications appear</li>
            <li>✅ Values persist after page refresh</li>
            <li>✅ No JavaScript errors in console</li>
        </ul>
    </div>

    <script>
        console.log('🔧 Redco Auto-Save Debug Test Page Loaded');
        console.log('📋 Instructions: Navigate to Redco Optimizer Settings page and run testRedcoAutoSave()');
    </script>
</body>
</html>
